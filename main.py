
from env import ENVIRONMENT
from cskb.cskb_operation import CSKBOperation
from database.mysql import MySQLDatabase

def get_cskb_agents_info() -> list:
    """获取CSKB知识库的agent_id和access_token"""
    db_name = 'svwcopilot'
    db = MySQLDatabase(db_name)
    sql = f'SELECT * FROM cskb_agent_key where env = \'{ENVIRONMENT}\''
    results = db.execute_query(sql)
    return results


if __name__ == '__main__':
    db = 'cskb'
    cskb_operation = CSKBOperation(db=db)
    # agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    agent = {'id': 8, 'agent_id': 'f69b984010fd9c6d03e1e7387938af0e', 'agent_name': '研发知识库', 'access_token': '3dde0816-3828-4447-9dfd-4ccf4fe9a243', 'env': 'prod'}
    agent_id = agent['agent_id']
    # query_date = '2023-07-05 19:54:24'
    results = cskb_operation.get_updated_cskb_results(agent=agent)
    print(results)
    # agents = get_cskb_agents_info()
    # print(agents)
    

    